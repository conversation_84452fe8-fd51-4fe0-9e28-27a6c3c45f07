#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Drug-Drug Interaction Prediction Model with Configurable Parameters
===================================================================
This script implements a machine learning model to predict drug-drug interactions (DDIs)
including both interaction type and severity level with configurable parameters for all algorithms.

Features:
- Configurable parameters for all algorithms
- Parameter results recording
- Model saving functionality
- Comprehensive parameter tuning
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, confusion_matrix, classification_report
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.linear_model import LogisticRegression
import xgboost as xgb
import lightgbm as lgb
import shap
import warnings
import re
import time
import os
import json
import pickle
from datetime import datetime
from collections import Counter

# Deep Learning and NLP imports
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Dropout, Conv1D, MaxPooling1D, LSTM, Embedding, Input, GlobalMaxPooling1D, Concatenate
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from tensorflow.keras.utils import to_categorical
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow successfully imported")
except ImportError as e:
    print(f"TensorFlow import error: {e}")
    print("Installing TensorFlow...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "tensorflow"])
    try:
        import tensorflow as tf
        from tensorflow.keras.models import Sequential, Model
        from tensorflow.keras.layers import Dense, Dropout, Conv1D, MaxPooling1D, LSTM, Embedding, Input, GlobalMaxPooling1D, Concatenate
        from tensorflow.keras.optimizers import Adam
        from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
        from tensorflow.keras.utils import to_categorical
        TENSORFLOW_AVAILABLE = True
        print("TensorFlow successfully installed and imported")
    except ImportError as e2:
        print(f"Failed to install/import TensorFlow: {e2}")
        TENSORFLOW_AVAILABLE = False

try:
    from transformers import AutoTokenizer, TFAutoModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Transformers not available, skipping transformer-related imports")
    TRANSFORMERS_AVAILABLE = False

try:
    import torch
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    print("PyTorch not available, skipping torch-related imports")
    TORCH_AVAILABLE = False

# Additional visualization imports
from sklearn.metrics import roc_curve, auc
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Suppress warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

# ============================================================================
# CONFIGURABLE PARAMETERS SECTION
# ============================================================================

class ModelParameters:
    """Class to store all configurable parameters for different algorithms"""

    def __init__(self):
        # SVM Parameters
        self.svm_params = {
            'C': 1.0,                    # Regularization parameter
            'kernel': 'rbf',             # Kernel type ('linear', 'poly', 'rbf', 'sigmoid')
            'degree': 3,                 # Degree for poly kernel
            'gamma': 'scale',            # Kernel coefficient ('scale', 'auto', or float)
            'coef0': 0.0,               # Independent term in kernel function
            'shrinking': True,           # Whether to use shrinking heuristic
            'probability': True,         # Whether to enable probability estimates
            'tol': 1e-3,                # Tolerance for stopping criterion
            'cache_size': 200,          # Size of kernel cache (MB)
            'class_weight': None,       # Weights associated with classes
            'verbose': False,           # Enable verbose output
            'max_iter': -1,             # Hard limit on iterations (-1 for no limit)
            'decision_function_shape': 'ovr',  # 'ovo' or 'ovr'
            'break_ties': False,        # Break ties according to confidence values
            'random_state': 42
        }

        # Naive Bayes Parameters
        self.nb_params = {
            'priors': None,             # Prior probabilities of classes
            'var_smoothing': 1e-9       # Portion of largest variance added to variances
        }

        # Logistic Regression Parameters
        self.lr_params = {
            'penalty': 'l2',            # Regularization penalty ('l1', 'l2', 'elasticnet', 'none')
            'dual': False,              # Dual or primal formulation
            'tol': 1e-4,               # Tolerance for stopping criteria
            'C': 1.0,                  # Inverse of regularization strength
            'fit_intercept': True,      # Whether to calculate intercept
            'intercept_scaling': 1,     # Scaling between synthetic feature weight
            'class_weight': None,       # Weights associated with classes
            'random_state': 42,
            'solver': 'lbfgs',         # Algorithm ('newton-cg', 'lbfgs', 'liblinear', 'sag', 'saga')
            'max_iter': 1000,          # Maximum number of iterations
            'multi_class': 'multinomial',  # Multiclass option ('auto', 'ovr', 'multinomial')
            'verbose': 0,              # Verbosity level
            'warm_start': False,       # Reuse solution of previous call
            'n_jobs': None,            # Number of CPU cores (-1 for all)
            'l1_ratio': None           # Elastic-Net mixing parameter
        }

        # XGBoost Parameters
        self.xgb_params = {
            'max_depth': 6,             # Maximum tree depth
            'learning_rate': 0.3,       # Boosting learning rate
            'n_estimators': 200,        # Number of gradient boosted trees
            'verbosity': 0,             # Verbosity of printing messages
            'objective': 'multi:softprob',  # Learning objective
            'booster': 'gbtree',        # Booster to use ('gbtree', 'gblinear', 'dart')
            'tree_method': 'auto',      # Tree construction algorithm
            'n_jobs': -1,              # Number of parallel threads
            'gamma': 0,                # Minimum loss reduction for split
            'min_child_weight': 1,      # Minimum sum of instance weight in child
            'max_delta_step': 0,        # Maximum delta step for each leaf output
            'subsample': 1,             # Subsample ratio of training instances
            'colsample_bytree': 1,      # Subsample ratio of columns when constructing tree
            'colsample_bylevel': 1,     # Subsample ratio of columns for each level
            'colsample_bynode': 1,      # Subsample ratio of columns for each node
            'reg_alpha': 0,             # L1 regularization term on weights
            'reg_lambda': 1,            # L2 regularization term on weights
            'scale_pos_weight': 1,      # Balancing of positive and negative weights
            'base_score': 0.5,          # Global bias
            'random_state': 42,
            'num_parallel_tree': 1,     # Number of parallel trees constructed
            'importance_type': 'gain',   # Feature importance type
            'gpu_id': -1,              # Device ordinal
            'validate_parameters': True  # Give warnings for unknown parameter
        }

        # LightGBM Parameters
        self.lgb_params = {
            'boosting_type': 'gbdt',    # Boosting type ('gbdt', 'dart', 'goss', 'rf')
            'num_leaves': 31,           # Maximum tree leaves for base learners
            'max_depth': -1,            # Maximum tree depth for base learners
            'learning_rate': 0.1,       # Boosting learning rate
            'n_estimators': 100,        # Number of boosted trees to fit
            'subsample_for_bin': 200000,  # Number of samples for constructing bins
            'objective': 'multiclass',   # Specify the learning objective
            'class_weight': None,       # Weights associated with classes
            'min_split_gain': 0.0,      # Minimum loss reduction required for split
            'min_child_weight': 1e-3,   # Minimum sum of instance weight in child
            'min_child_samples': 20,    # Minimum number of data needed in child
            'subsample': 1.0,           # Subsample ratio of training instance
            'subsample_freq': 0,        # Frequency of subsample
            'colsample_bytree': 1.0,    # Subsample ratio of columns when constructing tree
            'reg_alpha': 0.0,           # L1 regularization term on weights
            'reg_lambda': 0.0,          # L2 regularization term on weights
            'random_state': 42,
            'n_jobs': -1,              # Number of parallel threads
            'silent': True,             # Whether to print messages while running
            'importance_type': 'split'   # Type of feature importance
        }

        # CNN Parameters
        self.cnn_params = {
            'filters_1': 64,            # Number of filters in first conv layer
            'filters_2': 32,            # Number of filters in second conv layer
            'filters_3': 16,            # Number of filters in third conv layer
            'kernel_size': 3,           # Size of convolution kernel
            'pool_size': 2,             # Size of max pooling
            'dropout_conv': 0.3,        # Dropout rate for conv layers
            'dropout_dense': 0.5,       # Dropout rate for dense layers
            'dense_units_1': 128,       # Units in first dense layer
            'dense_units_2': 64,        # Units in second dense layer
            'learning_rate': 0.001,     # Learning rate for optimizer
            'batch_size': 32,           # Batch size for training
            'epochs': 50,               # Number of training epochs
            'validation_split': 0.2,    # Validation split ratio
            'early_stopping_patience': 15,  # Early stopping patience
            'reduce_lr_patience': 10,   # Reduce LR patience
            'reduce_lr_factor': 0.5,    # Reduce LR factor
            'min_lr': 1e-7             # Minimum learning rate
        }

        # LSTM Parameters
        self.lstm_params = {
            'lstm_units_1': 128,        # Units in first LSTM layer
            'lstm_units_2': 64,         # Units in second LSTM layer
            'dropout': 0.3,             # Dropout rate for LSTM
            'recurrent_dropout': 0.3,   # Recurrent dropout rate
            'dense_units_1': 128,       # Units in first dense layer
            'dense_units_2': 64,        # Units in second dense layer
            'dropout_dense': 0.5,       # Dropout rate for dense layers
            'learning_rate': 0.001,     # Learning rate for optimizer
            'batch_size': 32,           # Batch size for training
            'epochs': 50,               # Number of training epochs
            'validation_split': 0.2,    # Validation split ratio
            'early_stopping_patience': 15,  # Early stopping patience
            'reduce_lr_patience': 10,   # Reduce LR patience
            'reduce_lr_factor': 0.5,    # Reduce LR factor
            'min_lr': 1e-7             # Minimum learning rate
        }

        # BERT-Inspired Parameters
        self.bert_params = {
            'embedding_dim': 256,       # Embedding dimension
            'attention_heads': 3,       # Number of attention heads
            'attention_dim': 128,       # Dimension of each attention head
            'ff_dim_1': 512,           # First feed-forward dimension
            'ff_dim_2': 256,           # Second feed-forward dimension
            'residual_dim': 256,        # Residual connection dimension
            'final_dense_dim': 128,     # Final dense layer dimension
            'dropout_embedding': 0.3,   # Dropout for embedding layer
            'dropout_attention': 0.3,   # Dropout for attention layers
            'dropout_ff': 0.4,         # Dropout for feed-forward layers
            'dropout_final': 0.5,      # Dropout for final layers
            'learning_rate': 0.001,     # Learning rate for optimizer
            'batch_size': 32,           # Batch size for training
            'epochs': 50,               # Number of training epochs
            'validation_split': 0.2,    # Validation split ratio
            'early_stopping_patience': 15,  # Early stopping patience
            'reduce_lr_patience': 10,   # Reduce LR patience
            'reduce_lr_factor': 0.5,    # Reduce LR factor
            'min_lr': 1e-7             # Minimum learning rate
        }

    def save_parameters(self, filename=None):
        """Save all parameters to a JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_parameters_{timestamp}.json"

        params_dict = {
            'svm_params': self.svm_params,
            'nb_params': self.nb_params,
            'lr_params': self.lr_params,
            'xgb_params': self.xgb_params,
            'lgb_params': self.lgb_params,
            'cnn_params': self.cnn_params,
            'lstm_params': self.lstm_params,
            'bert_params': self.bert_params
        }

        with open(filename, 'w') as f:
            json.dump(params_dict, f, indent=4)

        print(f"Parameters saved to {filename}")
        return filename

    def load_parameters(self, filename):
        """Load parameters from a JSON file"""
        with open(filename, 'r') as f:
            params_dict = json.load(f)

        self.svm_params = params_dict.get('svm_params', self.svm_params)
        self.nb_params = params_dict.get('nb_params', self.nb_params)
        self.lr_params = params_dict.get('lr_params', self.lr_params)
        self.xgb_params = params_dict.get('xgb_params', self.xgb_params)
        self.lgb_params = params_dict.get('lgb_params', self.lgb_params)
        self.cnn_params = params_dict.get('cnn_params', self.cnn_params)
        self.lstm_params = params_dict.get('lstm_params', self.lstm_params)
        self.bert_params = params_dict.get('bert_params', self.bert_params)

        print(f"Parameters loaded from {filename}")

# Initialize parameters
model_params = ModelParameters()

# Try to load parameters from config file if it exists
try:
    model_params.load_parameters('model_config.json')
    print("Parameters loaded from model_config.json")
except FileNotFoundError:
    print("model_config.json not found, using default parameters")
    print("You can create model_config.json by running: model_params.save_parameters('model_config.json')")

# ============================================================================
# RESULTS RECORDING SYSTEM
# ============================================================================

class ResultsRecorder:
    """Class to record and manage experiment results"""

    def __init__(self):
        self.results = []
        self.experiment_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    def record_result(self, model_name, target_type, parameters, metrics, training_time, model_path=None):
        """Record a single experiment result"""
        result = {
            'experiment_id': self.experiment_id,
            'timestamp': datetime.now().isoformat(),
            'model_name': model_name,
            'target_type': target_type,
            'parameters': parameters,
            'metrics': metrics,
            'training_time': training_time,
            'model_path': model_path
        }
        self.results.append(result)

    def save_results(self, filename=None):
        """Save all results to a JSON file"""
        if filename is None:
            filename = f"experiment_results_{self.experiment_id}.json"

        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=4)

        print(f"Results saved to {filename}")
        return filename

    def get_best_results(self, metric='f1_score'):
        """Get best results for each model and target type"""
        best_results = {}

        for result in self.results:
            key = f"{result['model_name']}_{result['target_type']}"
            if key not in best_results or result['metrics'][metric] > best_results[key]['metrics'][metric]:
                best_results[key] = result

        return best_results

    def create_results_dataframe(self):
        """Create a pandas DataFrame from results"""
        if not self.results:
            return pd.DataFrame()

        df_data = []
        for result in self.results:
            row = {
                'Model': result['model_name'],
                'Target': result['target_type'],
                'Accuracy': result['metrics']['accuracy'],
                'F1_Score': result['metrics']['f1_score'],
                'Training_Time': result['training_time'],
                'Timestamp': result['timestamp']
            }
            # Add parameter information
            for param, value in result['parameters'].items():
                row[f'param_{param}'] = value

            df_data.append(row)

        return pd.DataFrame(df_data)

# Initialize results recorder
results_recorder = ResultsRecorder()

# ============================================================================
# TRAINING HISTORY TRACKER
# ============================================================================

class TrainingHistoryTracker:
    """Class to track training history for all models"""

    def __init__(self):
        self.training_histories = {}
        self.model_metrics = {}
        self.experiment_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    def record_training_history(self, model_name, target_type, history=None,
                              training_time=None, final_metrics=None,
                              model_type='traditional'):
        """Record training history for a model"""
        key = f"{model_name}_{target_type}"

        history_data = {
            'model_name': model_name,
            'target_type': target_type,
            'model_type': model_type,  # 'traditional', 'ensemble', 'deep_learning'
            'training_time': training_time,
            'final_metrics': final_metrics or {},
            'timestamp': datetime.now().isoformat()
        }

        if history is not None and hasattr(history, 'history'):
            # Deep learning model with Keras history
            history_data['epochs'] = len(history.history['loss'])
            history_data['training_loss'] = history.history['loss']
            history_data['validation_loss'] = history.history['val_loss']
            history_data['training_accuracy'] = history.history['accuracy']
            history_data['validation_accuracy'] = history.history['val_accuracy']
            history_data['best_epoch'] = np.argmin(history.history['val_loss']) + 1
            history_data['best_val_loss'] = min(history.history['val_loss'])
            history_data['best_val_accuracy'] = max(history.history['val_accuracy'])

        self.training_histories[key] = history_data

        # Also store in model metrics for easy access
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = {}
        self.model_metrics[model_name][target_type] = history_data

    def get_model_history(self, model_name, target_type):
        """Get training history for a specific model and target"""
        key = f"{model_name}_{target_type}"
        return self.training_histories.get(key, None)

    def get_all_histories_by_type(self, model_type):
        """Get all histories for a specific model type"""
        return {k: v for k, v in self.training_histories.items()
                if v['model_type'] == model_type}

    def create_summary_dataframe(self):
        """Create a summary DataFrame of all training histories"""
        summary_data = []

        for key, history in self.training_histories.items():
            row = {
                'Model': history['model_name'],
                'Target': history['target_type'],
                'Model_Type': history['model_type'],
                'Training_Time': history['training_time'],
                'Final_Accuracy': history['final_metrics'].get('accuracy', 'N/A'),
                'Final_F1_Score': history['final_metrics'].get('f1_score', 'N/A')
            }

            # Add deep learning specific metrics
            if history['model_type'] == 'deep_learning':
                row['Epochs'] = history.get('epochs', 'N/A')
                row['Best_Epoch'] = history.get('best_epoch', 'N/A')
                row['Best_Val_Loss'] = history.get('best_val_loss', 'N/A')
                row['Best_Val_Accuracy'] = history.get('best_val_accuracy', 'N/A')

            summary_data.append(row)

        return pd.DataFrame(summary_data)

    def plot_model_type_comparison(self):
        """Plot comparison between different model types"""
        df = self.create_summary_dataframe()

        if df.empty:
            print("No training history data available for plotting")
            return

        # Convert accuracy to numeric, handling 'N/A' values
        df['Final_Accuracy_Numeric'] = pd.to_numeric(df['Final_Accuracy'], errors='coerce')
        df['Final_F1_Score_Numeric'] = pd.to_numeric(df['Final_F1_Score'], errors='coerce')

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Plot 1: Training time by model type
        model_type_time = df.groupby('Model_Type')['Training_Time'].mean()
        axes[0, 0].bar(model_type_time.index, model_type_time.values,
                       color=['skyblue', 'lightcoral', 'lightgreen'])
        axes[0, 0].set_title('Average Training Time by Model Type')
        axes[0, 0].set_ylabel('Training Time (seconds)')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # Plot 2: Accuracy by model type
        model_type_acc = df.groupby('Model_Type')['Final_Accuracy_Numeric'].mean()
        axes[0, 1].bar(model_type_acc.index, model_type_acc.values,
                       color=['skyblue', 'lightcoral', 'lightgreen'])
        axes[0, 1].set_title('Average Accuracy by Model Type')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # Plot 3: F1 Score by model type
        model_type_f1 = df.groupby('Model_Type')['Final_F1_Score_Numeric'].mean()
        axes[1, 0].bar(model_type_f1.index, model_type_f1.values,
                       color=['skyblue', 'lightcoral', 'lightgreen'])
        axes[1, 0].set_title('Average F1 Score by Model Type')
        axes[1, 0].set_ylabel('F1 Score')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # Plot 4: Training time vs Accuracy scatter
        for model_type in df['Model_Type'].unique():
            subset = df[df['Model_Type'] == model_type]
            axes[1, 1].scatter(subset['Training_Time'], subset['Final_Accuracy_Numeric'],
                             label=model_type, alpha=0.7, s=100)

        axes[1, 1].set_xlabel('Training Time (seconds)')
        axes[1, 1].set_ylabel('Accuracy')
        axes[1, 1].set_title('Training Time vs Accuracy')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f'model_type_comparison_{self.experiment_id}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"Model type comparison saved as {filename}")

    def save_histories(self, filename=None):
        """Save all training histories to a JSON file"""
        if filename is None:
            filename = f"training_histories_{self.experiment_id}.json"

        # Convert numpy arrays to lists for JSON serialization
        serializable_histories = {}
        for key, history in self.training_histories.items():
            serializable_history = history.copy()
            for metric_key, metric_value in serializable_history.items():
                if isinstance(metric_value, np.ndarray):
                    serializable_history[metric_key] = metric_value.tolist()
                elif isinstance(metric_value, (np.integer, np.floating)):
                    serializable_history[metric_key] = float(metric_value)
            serializable_histories[key] = serializable_history

        with open(filename, 'w') as f:
            json.dump(serializable_histories, f, indent=4)

        print(f"Training histories saved to {filename}")
        return filename

# Initialize training history tracker
training_tracker = TrainingHistoryTracker()

# ============================================================================
# MODEL SAVING SYSTEM
# ============================================================================

class ModelSaver:
    """Class to save and load trained models"""

    def __init__(self):
        self.models_dir = "saved_models"
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)

    def save_sklearn_model(self, model, model_name, target_type, parameters):
        """Save sklearn model"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{model_name.lower().replace(' ', '_')}_{target_type.lower().replace(' ', '_')}_{timestamp}.pkl"
        filepath = os.path.join(self.models_dir, filename)

        model_data = {
            'model': model,
            'model_name': model_name,
            'target_type': target_type,
            'parameters': parameters,
            'timestamp': timestamp
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"Model saved to {filepath}")
        return filepath

    def save_keras_model(self, model, model_name, target_type, parameters):
        """Save Keras model"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{model_name.lower().replace(' ', '_').replace('-', '_')}_{target_type.lower().replace(' ', '_')}_{timestamp}.h5"
        model_filepath = os.path.join(self.models_dir, model_filename)

        # Save the model
        model.save(model_filepath)

        # Save metadata
        metadata_filename = f"{model_name.lower().replace(' ', '_').replace('-', '_')}_{target_type.lower().replace(' ', '_')}_{timestamp}_metadata.json"
        metadata_filepath = os.path.join(self.models_dir, metadata_filename)

        metadata = {
            'model_name': model_name,
            'target_type': target_type,
            'parameters': parameters,
            'timestamp': timestamp,
            'model_file': model_filename
        }

        with open(metadata_filepath, 'w') as f:
            json.dump(metadata, f, indent=4)

        print(f"Keras model saved to {model_filepath}")
        print(f"Metadata saved to {metadata_filepath}")
        return model_filepath

    def load_sklearn_model(self, filepath):
        """Load sklearn model"""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        return model_data

    def load_keras_model(self, model_filepath):
        """Load Keras model"""
        model = tf.keras.models.load_model(model_filepath)

        # Try to load metadata
        metadata_filepath = model_filepath.replace('.h5', '_metadata.json')
        metadata = None
        if os.path.exists(metadata_filepath):
            with open(metadata_filepath, 'r') as f:
                metadata = json.load(f)

        return model, metadata

# Initialize model saver
model_saver = ModelSaver()

# ============================================================================
# PARAMETER CLEANING FUNCTIONS
# ============================================================================

def clean_xgb_params(params):
    """Clean XGBoost parameters to remove None values that cause issues"""
    cleaned_params = {}

    # Parameters that should not be None
    skip_if_none = ['missing', 'monotone_constraints', 'interaction_constraints']

    for key, value in params.items():
        if key in skip_if_none and value is None:
            continue  # Skip None values for these parameters
        cleaned_params[key] = value

    return cleaned_params

def clean_lgb_params(params):
    """Clean LightGBM parameters to remove None values that cause issues"""
    cleaned_params = {}

    # Parameters that should not be None
    skip_if_none = ['class_weight']

    for key, value in params.items():
        if key in skip_if_none and value is None:
            continue  # Skip None values for these parameters
        cleaned_params[key] = value

    return cleaned_params

# 1. Data Loading and Exploration
print("Step 1: Data Loading and Exploration")
print("-" * 50)

# Load the dataset
df = pd.read_csv('/kaggle/input/molepure/MolePure Dataset.csv')

# Display basic information
print(f"Dataset shape: {df.shape}")
print("\nFirst few rows:")
print(df.head())

# Check for missing values
print("\nMissing values per column:")
print(df.isnull().sum())

# Check data types
print("\nData types:")
print(df.dtypes)

# Basic statistics
print("\nBasic statistics for numerical columns:")
print(df.describe())

# 2. Data Preprocessing
print("\nStep 2: Data Preprocessing")
print("-" * 50)

# Function to extract and process metabolic pathways
def process_metabolic_pathways(pathway_str):
    if pd.isna(pathway_str) or pathway_str == "Unknown":
        return []

    # Extract enzyme names (CYP*, UGT*, etc.)
    enzymes = re.findall(r'CYP\w+|UGT\w+|P-gp|BCRP', str(pathway_str))

    # If no enzymes found, return empty list
    if not enzymes:
        return []

    return enzymes

# Function to process transporter interactions
def process_transporter(transporter_str):
    if pd.isna(transporter_str) or transporter_str == "No Transporter" or transporter_str == "Unknown":
        return []

    # Extract transporter names
    transporters = re.findall(r'P-gp|BCRP|OATP\w+|OCT\w+', str(transporter_str))

    # If no transporters found, return empty list
    if not transporters:
        return []

    return transporters

# Function to extract percentage from plasma protein binding
def extract_percentage(binding_str):
    if pd.isna(binding_str) or binding_str == "Unknown":
        return np.nan

    # Extract percentage value
    match = re.search(r'(\d+)%', str(binding_str))
    if match:
        return float(match.group(1))
    return np.nan

# Apply preprocessing to the dataset
print("Preprocessing the dataset...")

# Process LogP values
df['LogP_A'] = pd.to_numeric(df['LogP_A'], errors='coerce')
df['LogP_B'] = pd.to_numeric(df['LogP_B'], errors='coerce')

# Process Plasma Protein Binding
df['Plasma_Protein_Binding_A_Value'] = df['Plasma_Protein_Binding_A'].apply(extract_percentage)
df['Plasma_Protein_Binding_B_Value'] = df['Plasma_Protein_Binding_B'].apply(extract_percentage)

# Process Metabolic Pathways
df['Metabolic_Pathways_A_List'] = df['Metabolic_Pathways_A'].apply(process_metabolic_pathways)
df['Metabolic_Pathways_B_List'] = df['Metabolic_Pathways_B'].apply(process_metabolic_pathways)

# Process Transporter Interactions
df['Transporter_Interaction_A_List'] = df['Transporter_Interaction_A'].apply(process_transporter)
df['Transporter_Interaction_B_List'] = df['Transporter_Interaction_B'].apply(process_transporter)

# 3. Feature Engineering
print("\nStep 3: Feature Engineering")
print("-" * 50)

# Create a set of all enzymes and transporters
all_enzymes = set()
all_transporters = set()

for enzymes in df['Metabolic_Pathways_A_List']:
    all_enzymes.update(enzymes)
for enzymes in df['Metabolic_Pathways_B_List']:
    all_enzymes.update(enzymes)

for transporters in df['Transporter_Interaction_A_List']:
    all_transporters.update(transporters)
for transporters in df['Transporter_Interaction_B_List']:
    all_transporters.update(transporters)

print(f"Total unique enzymes found: {len(all_enzymes)}")
print(f"Total unique transporters found: {len(all_transporters)}")

# Create binary features for enzymes and transporters
for enzyme in all_enzymes:
    df[f'Drug_A_{enzyme}'] = df['Metabolic_Pathways_A_List'].apply(lambda x: 1 if enzyme in x else 0)
    df[f'Drug_B_{enzyme}'] = df['Metabolic_Pathways_B_List'].apply(lambda x: 1 if enzyme in x else 0)

for transporter in all_transporters:
    df[f'Drug_A_{transporter}'] = df['Transporter_Interaction_A_List'].apply(lambda x: 1 if transporter in x else 0)
    df[f'Drug_B_{transporter}'] = df['Transporter_Interaction_B_List'].apply(lambda x: 1 if transporter in x else 0)

# Create features for shared pathways and transporters
df['Shared_Enzymes'] = df.apply(lambda row: len(set(row['Metabolic_Pathways_A_List']) & set(row['Metabolic_Pathways_B_List'])), axis=1)
df['Shared_Transporters'] = df.apply(lambda row: len(set(row['Transporter_Interaction_A_List']) & set(row['Transporter_Interaction_B_List'])), axis=1)

# One-hot encode categorical features
categorical_features = ['Pharmacodynamic_Class_A', 'Pharmacodynamic_Class_B', 'Therapeutic_Index_A', 'Therapeutic_Index_B']
df_encoded = pd.get_dummies(df, columns=categorical_features, dummy_na=True)

print(f"Dataset shape after feature engineering: {df_encoded.shape}")

# 4. Prepare data for modeling
print("\nStep 4: Preparing Data for Modeling")
print("-" * 50)

# Define features and target variables
# Exclude original text columns and lists that have been processed
exclude_cols = ['DDInterID_A', 'Drug_A', 'Metabolic_Pathways_A', 'Transporter_Interaction_A', 'Plasma_Protein_Binding_A',
                'DDInterID_B', 'Drug_B', 'Metabolic_Pathways_B', 'Transporter_Interaction_B', 'Plasma_Protein_Binding_B',
                'Metabolic_Pathways_A_List', 'Metabolic_Pathways_B_List', 'Transporter_Interaction_A_List', 'Transporter_Interaction_B_List',
                'interaction_type', 'Level']

# Features
X = df_encoded.drop(exclude_cols, axis=1)

# Fill missing values
X = X.fillna(-1)  # Use -1 to indicate missing values for numerical features

# Target variables
y_type = df['interaction_type']
y_level = df['Level']

# Encode target variables
label_encoder_type = LabelEncoder()
label_encoder_level = LabelEncoder()

y_type_encoded = label_encoder_type.fit_transform(y_type)
y_level_encoded = label_encoder_level.fit_transform(y_level)

print(f"Number of features: {X.shape[1]}")
print(f"Interaction types: {label_encoder_type.classes_}")
print(f"Severity levels: {label_encoder_level.classes_}")

# Split the data
# Remove stratification to handle classes with very few samples
X_train, X_test, y_type_train, y_type_test, y_level_train, y_level_test = train_test_split(
    X, y_type_encoded, y_level_encoded, test_size=0.2, random_state=42
)

print(f"Training set size: {X_train.shape}")
print(f"Testing set size: {X_test.shape}")

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Print class distribution
print("\nInteraction Type Distribution:")
print(pd.Series(y_type_train).value_counts())
print("\nSeverity Level Distribution:")
print(pd.Series(y_level_train).value_counts())

# Now we're ready to implement the models in the next steps
print("\nData preparation complete. Ready for model implementation.")

# 5. Model Implementation and Training
print("\nStep 5: Model Implementation and Training")
print("-" * 50)

# Function to save a summary report
def save_model_summary(model_name, target_type, accuracy, f1_score, report, training_time, model_params=None):
    """Save a summary report for a trained model."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"model_summary_{model_name.lower().replace(' ', '_')}_{target_type.lower().replace(' ', '_')}_{timestamp}.txt"

    with open(filename, 'w') as f:
        f.write(f"=== {model_name} Model Summary for {target_type} ===\n")
        f.write(f"Date and Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        if model_params:
            f.write("=== Model Parameters ===\n")
            for param, value in model_params.items():
                f.write(f"{param}: {value}\n")
            f.write("\n")

        f.write("=== Performance Metrics ===\n")
        f.write(f"Accuracy: {accuracy:.4f}\n")
        f.write(f"F1 Score: {f1_score:.4f}\n")
        f.write(f"Training Time: {training_time:.2f} seconds\n\n")

        f.write("=== Classification Report ===\n")
        f.write(report)

    print(f"Model summary saved to {filename}")

# Function to train and evaluate a model
def train_and_evaluate_model(model, X_train, X_test, y_train, y_test, model_name, target_type, parameters=None, model_type='traditional'):
    print(f"\nTraining {model_name} for {target_type}...")

    # Start timing
    start_time = time.time()

    # Train the model
    model.fit(X_train, y_train)

    # Calculate training time
    training_time = time.time() - start_time
    print(f"Training completed in {training_time:.2f} seconds")

    # Make predictions
    y_pred = model.predict(X_test)

    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, average='weighted')

    print(f"{model_name} - {target_type} Accuracy: {accuracy:.4f}")
    print(f"{model_name} - {target_type} F1 Score: {f1:.4f}")

    # Generate classification report
    report = classification_report(y_test, y_pred)
    print(f"\nClassification Report for {model_name} - {target_type}:")
    print(report)

    # Get model parameters
    if parameters is None:
        parameters = getattr(model, 'get_params', lambda: {})()

    # Save model summary
    save_model_summary(model_name, target_type, accuracy, f1, report, training_time, parameters)

    # Record results
    metrics = {
        'accuracy': accuracy,
        'f1_score': f1
    }

    # Save model
    model_path = model_saver.save_sklearn_model(model, model_name, target_type, parameters)

    # Record experiment result
    results_recorder.record_result(model_name, target_type, parameters, metrics, training_time, model_path)

    # Record training history
    training_tracker.record_training_history(
        model_name=model_name,
        target_type=target_type,
        history=None,  # No Keras history for traditional models
        training_time=training_time,
        final_metrics=metrics,
        model_type=model_type
    )

    return model, accuracy, f1, y_pred

# 5.1 Support Vector Machine (SVM)
print("\n5.1 Support Vector Machine (SVM)")
print("-" * 30)

# SVM for interaction type prediction
print("Current SVM parameters:")
for param, value in model_params.svm_params.items():
    print(f"  {param}: {value}")

svm_type = SVC(**model_params.svm_params)
svm_type_model, svm_type_acc, svm_type_f1, svm_type_pred = train_and_evaluate_model(
    svm_type, X_train_scaled, X_test_scaled, y_type_train, y_type_test, "SVM", "Interaction Type", model_params.svm_params, 'traditional'
)

# SVM for severity level prediction
svm_level = SVC(**model_params.svm_params)
svm_level_model, svm_level_acc, svm_level_f1, svm_level_pred = train_and_evaluate_model(
    svm_level, X_train_scaled, X_test_scaled, y_level_train, y_level_test, "SVM", "Severity Level", model_params.svm_params, 'traditional'
)

# 5.2 Naive Bayes
print("\n5.2 Naive Bayes")
print("-" * 30)

# Naive Bayes for interaction type prediction
print("Current Naive Bayes parameters:")
for param, value in model_params.nb_params.items():
    print(f"  {param}: {value}")

nb_type = GaussianNB(**model_params.nb_params)
nb_type_model, nb_type_acc, nb_type_f1, nb_type_pred = train_and_evaluate_model(
    nb_type, X_train_scaled, X_test_scaled, y_type_train, y_type_test, "Naive Bayes", "Interaction Type", model_params.nb_params, 'traditional'
)

# Naive Bayes for severity level prediction
nb_level = GaussianNB(**model_params.nb_params)
nb_level_model, nb_level_acc, nb_level_f1, nb_level_pred = train_and_evaluate_model(
    nb_level, X_train_scaled, X_test_scaled, y_level_train, y_level_test, "Naive Bayes", "Severity Level", model_params.nb_params, 'traditional'
)

# 5.3 Logistic Regression
print("\n5.3 Logistic Regression")
print("-" * 30)

# Logistic Regression for interaction type prediction
print("Current Logistic Regression parameters:")
for param, value in model_params.lr_params.items():
    print(f"  {param}: {value}")

lr_type = LogisticRegression(**model_params.lr_params)
lr_type_model, lr_type_acc, lr_type_f1, lr_type_pred = train_and_evaluate_model(
    lr_type, X_train_scaled, X_test_scaled, y_type_train, y_type_test, "Logistic Regression", "Interaction Type", model_params.lr_params, 'traditional'
)

# Logistic Regression for severity level prediction
lr_level = LogisticRegression(**model_params.lr_params)
lr_level_model, lr_level_acc, lr_level_f1, lr_level_pred = train_and_evaluate_model(
    lr_level, X_train_scaled, X_test_scaled, y_level_train, y_level_test, "Logistic Regression", "Severity Level", model_params.lr_params, 'traditional'
)

# 5.4 XGBoost
print("\n5.4 XGBoost")
print("-" * 30)

# XGBoost for interaction type prediction
print("Current XGBoost parameters:")
for param, value in model_params.xgb_params.items():
    print(f"  {param}: {value}")

# Clean XGBoost parameters to avoid None value issues
cleaned_xgb_params = clean_xgb_params(model_params.xgb_params)
print("Cleaned XGBoost parameters (removed None values):")
for param, value in cleaned_xgb_params.items():
    print(f"  {param}: {value}")

xgb_type = xgb.XGBClassifier(**cleaned_xgb_params)
xgb_type_model, xgb_type_acc, xgb_type_f1, xgb_type_pred = train_and_evaluate_model(
    xgb_type, X_train_scaled, X_test_scaled, y_type_train, y_type_test, "XGBoost", "Interaction Type", cleaned_xgb_params, 'ensemble'
)

# XGBoost for severity level prediction
xgb_level = xgb.XGBClassifier(**cleaned_xgb_params)
xgb_level_model, xgb_level_acc, xgb_level_f1, xgb_level_pred = train_and_evaluate_model(
    xgb_level, X_train_scaled, X_test_scaled, y_level_train, y_level_test, "XGBoost", "Severity Level", cleaned_xgb_params, 'ensemble'
)

# 5.5 LightGBM
print("\n5.5 LightGBM")
print("-" * 30)

# LightGBM for interaction type prediction
print("Current LightGBM parameters:")
for param, value in model_params.lgb_params.items():
    print(f"  {param}: {value}")

# Clean LightGBM parameters to avoid None value issues
cleaned_lgb_params = clean_lgb_params(model_params.lgb_params)
print("Cleaned LightGBM parameters (removed None values):")
for param, value in cleaned_lgb_params.items():
    print(f"  {param}: {value}")

lgb_type = lgb.LGBMClassifier(**cleaned_lgb_params)
lgb_type_model, lgb_type_acc, lgb_type_f1, lgb_type_pred = train_and_evaluate_model(
    lgb_type, X_train_scaled, X_test_scaled, y_type_train, y_type_test, "LightGBM", "Interaction Type", cleaned_lgb_params, 'ensemble'
)

# LightGBM for severity level prediction
lgb_level = lgb.LGBMClassifier(**cleaned_lgb_params)
lgb_level_model, lgb_level_acc, lgb_level_f1, lgb_level_pred = train_and_evaluate_model(
    lgb_level, X_train_scaled, X_test_scaled, y_level_train, y_level_test, "LightGBM", "Severity Level", cleaned_lgb_params, 'ensemble'
)

# 5.6 Deep Learning Models
print("\n5.6 Deep Learning Models")
print("-" * 30)

# Check if TensorFlow is available
if not TENSORFLOW_AVAILABLE:
    print("TensorFlow is not available. Skipping deep learning models.")
    # Create dummy variables for consistency
    cnn_type_acc = cnn_level_acc = 0.0
    cnn_type_f1 = cnn_level_f1 = 0.0
    lstm_type_acc = lstm_level_acc = 0.0
    lstm_type_f1 = lstm_level_f1 = 0.0
    bert_type_acc = bert_level_acc = 0.0
    bert_type_f1 = bert_level_f1 = 0.0
    cnn_type_pred = cnn_level_pred = np.array([])
    lstm_type_pred = lstm_level_pred = np.array([])
    bert_type_pred = bert_level_pred = np.array([])
    cnn_type_history = cnn_level_history = None
    lstm_type_history = lstm_level_history = None
    bert_type_history = bert_level_history = None
else:
    # Set random seeds for reproducibility
    tf.random.set_seed(42)
    np.random.seed(42)

    # Prepare data for deep learning models
    n_features = X_train_scaled.shape[1]
    n_type_classes = len(np.unique(y_type_train))
    n_level_classes = len(np.unique(y_level_train))

    # Convert to categorical for neural networks
    y_type_train_cat = to_categorical(y_type_train, num_classes=n_type_classes)
    y_type_test_cat = to_categorical(y_type_test, num_classes=n_type_classes)
    y_level_train_cat = to_categorical(y_level_train, num_classes=n_level_classes)
    y_level_test_cat = to_categorical(y_level_test, num_classes=n_level_classes)

    # Function to create CNN model
    def create_cnn_model(input_shape, num_classes, params=None, model_name="CNN"):
        """Create a 1D CNN model for tabular data"""
        if params is None:
            params = model_params.cnn_params

        print(f"Creating {model_name} model...")
        print("Current CNN parameters:")
        for param, value in params.items():
            print(f"  {param}: {value}")

        model = Sequential([
            # Reshape input for 1D convolution
            tf.keras.layers.Reshape((input_shape, 1), input_shape=(input_shape,)),

            # First convolutional block
            Conv1D(filters=params['filters_1'], kernel_size=params['kernel_size'], activation='relu', padding='same'),
            Dropout(params['dropout_conv']),
            Conv1D(filters=params['filters_2'], kernel_size=params['kernel_size'], activation='relu', padding='same'),
            MaxPooling1D(pool_size=params['pool_size']),
            Dropout(params['dropout_conv']),

            # Second convolutional block
            Conv1D(filters=params['filters_3'], kernel_size=params['kernel_size'], activation='relu', padding='same'),
            GlobalMaxPooling1D(),

            # Dense layers
            Dense(params['dense_units_1'], activation='relu'),
            Dropout(params['dropout_dense']),
            Dense(params['dense_units_2'], activation='relu'),
            Dropout(params['dropout_conv']),
            Dense(num_classes, activation='softmax')
        ])

        model.compile(
            optimizer=Adam(learning_rate=params['learning_rate']),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    # Function to create LSTM model
    def create_lstm_model(input_shape, num_classes, params=None, model_name="LSTM"):
        """Create an LSTM model for sequential pattern recognition"""
        if params is None:
            params = model_params.lstm_params

        print(f"Creating {model_name} model...")
        print("Current LSTM parameters:")
        for param, value in params.items():
            print(f"  {param}: {value}")

        model = Sequential([
            # Reshape input for LSTM
            tf.keras.layers.Reshape((input_shape, 1), input_shape=(input_shape,)),

            # LSTM layers
            LSTM(params['lstm_units_1'], return_sequences=True, dropout=params['dropout'], recurrent_dropout=params['recurrent_dropout']),
            LSTM(params['lstm_units_2'], return_sequences=False, dropout=params['dropout'], recurrent_dropout=params['recurrent_dropout']),

            # Dense layers
            Dense(params['dense_units_1'], activation='relu'),
            Dropout(params['dropout_dense']),
            Dense(params['dense_units_2'], activation='relu'),
            Dropout(params['dropout']),
            Dense(num_classes, activation='softmax')
        ])

        model.compile(
            optimizer=Adam(learning_rate=params['learning_rate']),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    # Function to create BERT-inspired model for tabular data
    def create_bert_inspired_model(input_shape, num_classes, params=None, model_name="BERT-Inspired"):
        """Create a BERT-inspired transformer model for tabular data"""
        if params is None:
            params = model_params.bert_params

        print(f"Creating {model_name} model...")
        print("Current BERT-Inspired parameters:")
        for param, value in params.items():
            print(f"  {param}: {value}")

        # Input layer
        inputs = Input(shape=(input_shape,))

        # Embedding-like dense layer
        x = Dense(params['embedding_dim'], activation='relu')(inputs)
        x = Dropout(params['dropout_embedding'])(x)

        # Multi-head attention simulation with dense layers
        attention_heads = []
        for i in range(params['attention_heads']):
            attention_head = Dense(params['attention_dim'], activation='relu', name=f'attention_head_{i+1}')(x)
            attention_heads.append(attention_head)

        # Concatenate attention heads
        attention_concat = Concatenate()(attention_heads)
        attention_concat = Dropout(params['dropout_attention'])(attention_concat)

        # Feed-forward network
        ff_1 = Dense(params['ff_dim_1'], activation='relu')(attention_concat)
        ff_1 = Dropout(params['dropout_ff'])(ff_1)
        ff_2 = Dense(params['ff_dim_2'], activation='relu')(ff_1)
        ff_2 = Dropout(params['dropout_attention'])(ff_2)

        # Residual connection
        residual = Dense(params['residual_dim'], activation='relu')(inputs)
        combined = tf.keras.layers.Add()([ff_2, residual])

        # Final classification layers
        x = Dense(params['final_dense_dim'], activation='relu')(combined)
        x = Dropout(params['dropout_final'])(x)
        outputs = Dense(num_classes, activation='softmax')(x)

        model = Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer=Adam(learning_rate=params['learning_rate']),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    # Function to train deep learning model
    def train_deep_model(model, X_train, X_test, y_train, y_test, model_name, target_type, params=None):
        """Train a deep learning model with early stopping"""
        if params is None:
            # Use default parameters based on model type
            if 'CNN' in model_name:
                params = model_params.cnn_params
            elif 'LSTM' in model_name:
                params = model_params.lstm_params
            elif 'BERT' in model_name:
                params = model_params.bert_params
            else:
                params = model_params.cnn_params  # Default fallback

        print(f"\nTraining {model_name} for {target_type}...")

        # Callbacks
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=params['early_stopping_patience'],
            restore_best_weights=True,
            verbose=1
        )

        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss',
            factor=params['reduce_lr_factor'],
            patience=params['reduce_lr_patience'],
            min_lr=params['min_lr'],
            verbose=1
        )

        # Start timing
        start_time = time.time()

        # Train the model
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=params['epochs'],
            batch_size=params['batch_size'],
            callbacks=[early_stopping, reduce_lr],
            verbose=1
        )

        # Calculate training time
        training_time = time.time() - start_time
        print(f"Training completed in {training_time:.2f} seconds")

        # Make predictions
        y_pred_proba = model.predict(X_test)
        y_pred = np.argmax(y_pred_proba, axis=1)
        y_true = np.argmax(y_test, axis=1)

        # Calculate metrics
        accuracy = accuracy_score(y_true, y_pred)
        f1 = f1_score(y_true, y_pred, average='weighted')

        print(f"{model_name} - {target_type} Accuracy: {accuracy:.4f}")
        print(f"{model_name} - {target_type} F1 Score: {f1:.4f}")

        # Generate classification report
        report = classification_report(y_true, y_pred)
        print(f"\nClassification Report for {model_name} - {target_type}:")
        print(report)

        # Prepare model parameters for saving
        model_params_dict = {
            'total_params': model.count_params(),
            'epochs_trained': len(history.history['loss']),
            'final_train_loss': history.history['loss'][-1],
            'final_val_loss': history.history['val_loss'][-1]
        }
        model_params_dict.update(params)  # Add training parameters

        # Save model summary
        save_model_summary(model_name, target_type, accuracy, f1, report, training_time, model_params_dict)

        # Record results
        metrics = {
            'accuracy': accuracy,
            'f1_score': f1
        }

        # Save model
        model_path = model_saver.save_keras_model(model, model_name, target_type, model_params_dict)

        # Record experiment result
        results_recorder.record_result(model_name, target_type, model_params_dict, metrics, training_time, model_path)

        # Record training history
        training_tracker.record_training_history(
            model_name=model_name,
            target_type=target_type,
            history=history,  # Keras history object
            training_time=training_time,
            final_metrics=metrics,
            model_type='deep_learning'
        )

        return model, accuracy, f1, y_pred, history

    # Train CNN models
    print("\n5.6.1 CNN Models")
    print("-" * 20)

    # CNN for interaction type prediction
    cnn_type_model = create_cnn_model(n_features, n_type_classes, model_params.cnn_params, "CNN")
    cnn_type_trained, cnn_type_acc, cnn_type_f1, cnn_type_pred, cnn_type_history = train_deep_model(
        cnn_type_model, X_train_scaled, X_test_scaled, y_type_train_cat, y_type_test_cat,
        "CNN", "Interaction Type", model_params.cnn_params
    )

    # CNN for severity level prediction
    cnn_level_model = create_cnn_model(n_features, n_level_classes, model_params.cnn_params, "CNN")
    cnn_level_trained, cnn_level_acc, cnn_level_f1, cnn_level_pred, cnn_level_history = train_deep_model(
        cnn_level_model, X_train_scaled, X_test_scaled, y_level_train_cat, y_level_test_cat,
        "CNN", "Severity Level", model_params.cnn_params
    )

    # Train LSTM models
    print("\n5.6.2 LSTM Models")
    print("-" * 20)

    # LSTM for interaction type prediction
    lstm_type_model = create_lstm_model(n_features, n_type_classes, model_params.lstm_params, "LSTM")
    lstm_type_trained, lstm_type_acc, lstm_type_f1, lstm_type_pred, lstm_type_history = train_deep_model(
        lstm_type_model, X_train_scaled, X_test_scaled, y_type_train_cat, y_type_test_cat,
        "LSTM", "Interaction Type", model_params.lstm_params
    )

    # LSTM for severity level prediction
    lstm_level_model = create_lstm_model(n_features, n_level_classes, model_params.lstm_params, "LSTM")
    lstm_level_trained, lstm_level_acc, lstm_level_f1, lstm_level_pred, lstm_level_history = train_deep_model(
        lstm_level_model, X_train_scaled, X_test_scaled, y_level_train_cat, y_level_test_cat,
        "LSTM", "Severity Level", model_params.lstm_params
    )

    # Train BERT-inspired models
    print("\n5.6.3 BERT-Inspired Models")
    print("-" * 25)

    # BERT-inspired for interaction type prediction
    bert_type_model = create_bert_inspired_model(n_features, n_type_classes, model_params.bert_params, "BERT-Inspired")
    bert_type_trained, bert_type_acc, bert_type_f1, bert_type_pred, bert_type_history = train_deep_model(
        bert_type_model, X_train_scaled, X_test_scaled, y_type_train_cat, y_type_test_cat,
        "BERT-Inspired", "Interaction Type", model_params.bert_params
    )

    # BERT-inspired for severity level prediction
    bert_level_model = create_bert_inspired_model(n_features, n_level_classes, model_params.bert_params, "BERT-Inspired")
    bert_level_trained, bert_level_acc, bert_level_f1, bert_level_pred, bert_level_history = train_deep_model(
        bert_level_model, X_train_scaled, X_test_scaled, y_level_train_cat, y_level_test_cat,
        "BERT-Inspired", "Severity Level", model_params.bert_params
    )

# 6. Training History Analysis and Enhanced Visualizations
print("\nStep 6: Training History Analysis and Enhanced Visualizations")
print("-" * 50)

# Generate comprehensive training history analysis
print("\nGenerating comprehensive training history analysis...")
training_summary_df = training_tracker.create_summary_dataframe()

if not training_summary_df.empty:
    print("\nTraining History Summary:")
    print(training_summary_df.to_string(index=False))

    # Save training history summary
    training_summary_filename = f"training_summary_{training_tracker.experiment_id}.csv"
    training_summary_df.to_csv(training_summary_filename, index=False)
    print(f"\nTraining summary saved to {training_summary_filename}")

    # Plot model type comparison
    training_tracker.plot_model_type_comparison()

    # Save all training histories
    training_tracker.save_histories()
else:
    print("No training history data available yet.")

print("\nGenerating enhanced visualizations...")

# Function to create enhanced confusion matrix heatmap
def plot_confusion_matrix_heatmap(y_true, y_pred, labels, model_name, target_type):
    """Create and save enhanced confusion matrix heatmap with percentages and better formatting"""
    cm = confusion_matrix(y_true, y_pred)

    # Calculate percentages
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

    # Create figure with larger size for better readability
    plt.figure(figsize=(12, 10))

    # Create annotations that show both count and percentage
    annotations = []
    for i in range(cm.shape[0]):
        row_annotations = []
        for j in range(cm.shape[1]):
            count = cm[i, j]
            percentage = cm_percent[i, j]
            if count > 0:
                annotation = f'{count}\n({percentage:.1f}%)'
            else:
                annotation = '0\n(0.0%)'
            row_annotations.append(annotation)
        annotations.append(row_annotations)

    # Create heatmap with improved styling
    ax = sns.heatmap(cm,
                     annot=annotations,
                     fmt='',
                     cmap='Blues',
                     xticklabels=labels,
                     yticklabels=labels,
                     cbar_kws={'label': 'Number of Samples'},
                     square=True,
                     linewidths=0.5,
                     linecolor='white')

    # Improve title and labels
    plt.title(f'Confusion Matrix - {model_name}\n{target_type} Prediction',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Predicted Labels', fontsize=14, fontweight='bold')
    plt.ylabel('True Labels', fontsize=14, fontweight='bold')

    # Rotate labels for better readability
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)

    # Add accuracy information
    accuracy = np.trace(cm) / np.sum(cm)
    plt.figtext(0.02, 0.02, f'Overall Accuracy: {accuracy:.3f}',
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

    # Adjust layout
    plt.tight_layout()

    # Save with descriptive filename
    filename = f'confusion_matrix_{model_name.lower().replace(" ", "_").replace("-", "_")}_{target_type.lower().replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    print(f"Enhanced confusion matrix saved as {filename}")

    # Print detailed classification metrics
    print(f"\nDetailed Metrics for {model_name} - {target_type}:")
    print("-" * 50)

    # Calculate per-class metrics
    for i, label in enumerate(labels):
        if i < len(cm):
            tp = cm[i, i]  # True positives
            fp = cm[:, i].sum() - tp  # False positives
            fn = cm[i, :].sum() - tp  # False negatives
            tn = cm.sum() - tp - fp - fn  # True negatives

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

            print(f"{label:15} | Precision: {precision:.3f} | Recall: {recall:.3f} | F1: {f1:.3f}")

    return cm, cm_percent

# Function to plot ROC curves
def plot_roc_curves(models_data, target_type):
    """Plot ROC curves for multiple models"""
    plt.figure(figsize=(12, 8))

    for model_name, y_true, y_pred_proba in models_data:
        if len(np.unique(y_true)) == 2:  # Binary classification
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba[:, 1])
            roc_auc = auc(fpr, tpr)
            plt.plot(fpr, tpr, label=f'{model_name} (AUC = {roc_auc:.3f})')

    plt.plot([0, 1], [0, 1], 'k--', label='Random Classifier')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(f'ROC Curves - {target_type}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    filename = f'roc_curves_{target_type.lower().replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"ROC curves saved as {filename}")

# Function to plot training history
def plot_training_history(histories, model_names, target_type):
    """Plot training history for deep learning models"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Plot training and validation loss
    for i, (history, model_name) in enumerate(zip(histories, model_names)):
        axes[0, 0].plot(history.history['loss'], label=f'{model_name} Train')
        axes[0, 0].plot(history.history['val_loss'], label=f'{model_name} Val')

    axes[0, 0].set_title('Model Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # Plot training and validation accuracy
    for i, (history, model_name) in enumerate(zip(histories, model_names)):
        axes[0, 1].plot(history.history['accuracy'], label=f'{model_name} Train')
        axes[0, 1].plot(history.history['val_accuracy'], label=f'{model_name} Val')

    axes[0, 1].set_title('Model Accuracy')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # Plot final epoch comparison
    final_train_acc = [h.history['accuracy'][-1] for h in histories]
    final_val_acc = [h.history['val_accuracy'][-1] for h in histories]

    x = np.arange(len(model_names))
    width = 0.35

    axes[1, 0].bar(x - width/2, final_train_acc, width, label='Train Accuracy')
    axes[1, 0].bar(x + width/2, final_val_acc, width, label='Validation Accuracy')
    axes[1, 0].set_title('Final Epoch Accuracy Comparison')
    axes[1, 0].set_xlabel('Models')
    axes[1, 0].set_ylabel('Accuracy')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(model_names, rotation=45)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # Plot final epoch loss comparison
    final_train_loss = [h.history['loss'][-1] for h in histories]
    final_val_loss = [h.history['val_loss'][-1] for h in histories]

    axes[1, 1].bar(x - width/2, final_train_loss, width, label='Train Loss')
    axes[1, 1].bar(x + width/2, final_val_loss, width, label='Validation Loss')
    axes[1, 1].set_title('Final Epoch Loss Comparison')
    axes[1, 1].set_xlabel('Models')
    axes[1, 1].set_ylabel('Loss')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(model_names, rotation=45)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    filename = f'training_history_{target_type.lower().replace(" ", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"Training history saved as {filename}")

# 7. Model Comparison (Updated)
print("\nStep 7: Comprehensive Model Comparison")
print("-" * 50)

# Create a comprehensive dataframe to compare all model performance
all_model_names = ["SVM", "Naive Bayes", "Logistic Regression", "XGBoost", "LightGBM", "CNN", "LSTM", "BERT-Inspired"]
all_interaction_type_acc = [svm_type_acc, nb_type_acc, lr_type_acc, xgb_type_acc, lgb_type_acc, cnn_type_acc, lstm_type_acc, bert_type_acc]
all_interaction_type_f1 = [svm_type_f1, nb_type_f1, lr_type_f1, xgb_type_f1, lgb_type_f1, cnn_type_f1, lstm_type_f1, bert_type_f1]
all_severity_level_acc = [svm_level_acc, nb_level_acc, lr_level_acc, xgb_level_acc, lgb_level_acc, cnn_level_acc, lstm_level_acc, bert_level_acc]
all_severity_level_f1 = [svm_level_f1, nb_level_f1, lr_level_f1, xgb_level_f1, lgb_level_f1, cnn_level_f1, lstm_level_f1, bert_level_f1]

comprehensive_comparison_df = pd.DataFrame({
    'Model': all_model_names,
    'Interaction Type Accuracy': all_interaction_type_acc,
    'Interaction Type F1 Score': all_interaction_type_f1,
    'Severity Level Accuracy': all_severity_level_acc,
    'Severity Level F1 Score': all_severity_level_f1
})

print("\nComprehensive Model Performance Comparison:")
print(comprehensive_comparison_df)

# Find the best model for each task
best_type_model = comprehensive_comparison_df.loc[comprehensive_comparison_df['Interaction Type F1 Score'].idxmax()]['Model']
best_level_model = comprehensive_comparison_df.loc[comprehensive_comparison_df['Severity Level F1 Score'].idxmax()]['Model']

print(f"\nBest model for Interaction Type prediction: {best_type_model}")
print(f"Best model for Severity Level prediction: {best_level_model}")

# Create visualizations
print("\nGenerating comprehensive visualizations...")

# Plot confusion matrices for best models
type_labels = label_encoder_type.classes_
level_labels = label_encoder_level.classes_

# Get predictions for confusion matrices
best_models_predictions = {
    'SVM': (svm_type_pred, svm_level_pred),
    'Naive Bayes': (nb_type_pred, nb_level_pred),
    'Logistic Regression': (lr_type_pred, lr_level_pred),
    'XGBoost': (xgb_type_pred, xgb_level_pred),
    'LightGBM': (lgb_type_pred, lgb_level_pred),
    'CNN': (cnn_type_pred, cnn_level_pred),
    'LSTM': (lstm_type_pred, lstm_level_pred),
    'BERT-Inspired': (bert_type_pred, bert_level_pred)
}

# Plot confusion matrices for top 3 models for each task
top_3_type_models = comprehensive_comparison_df.nlargest(3, 'Interaction Type F1 Score')['Model'].tolist()
top_3_level_models = comprehensive_comparison_df.nlargest(3, 'Severity Level F1 Score')['Model'].tolist()

print(f"\nTop 3 models for Interaction Type: {top_3_type_models}")
print(f"Top 3 models for Severity Level: {top_3_level_models}")

# Generate confusion matrices for top models
for model_name in top_3_type_models:
    if model_name in best_models_predictions:
        type_pred, _ = best_models_predictions[model_name]
        plot_confusion_matrix_heatmap(y_type_test, type_pred, type_labels, model_name, "Interaction Type")

for model_name in top_3_level_models:
    if model_name in best_models_predictions:
        _, level_pred = best_models_predictions[model_name]
        plot_confusion_matrix_heatmap(y_level_test, level_pred, level_labels, model_name, "Severity Level")

# Plot training histories for deep learning models
dl_histories_type = [cnn_type_history, lstm_type_history, bert_type_history]
dl_histories_level = [cnn_level_history, lstm_level_history, bert_level_history]
dl_model_names = ["CNN", "LSTM", "BERT-Inspired"]

plot_training_history(dl_histories_type, dl_model_names, "Interaction Type")
plot_training_history(dl_histories_level, dl_model_names, "Severity Level")

# Create comprehensive performance comparison plots
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# Accuracy comparison for Interaction Type
axes[0, 0].bar(all_model_names, all_interaction_type_acc, color='skyblue', alpha=0.7)
axes[0, 0].set_title('Interaction Type Prediction - Accuracy Comparison')
axes[0, 0].set_ylabel('Accuracy')
axes[0, 0].tick_params(axis='x', rotation=45)
axes[0, 0].grid(True, alpha=0.3)

# F1 Score comparison for Interaction Type
axes[0, 1].bar(all_model_names, all_interaction_type_f1, color='lightcoral', alpha=0.7)
axes[0, 1].set_title('Interaction Type Prediction - F1 Score Comparison')
axes[0, 1].set_ylabel('F1 Score')
axes[0, 1].tick_params(axis='x', rotation=45)
axes[0, 1].grid(True, alpha=0.3)

# Accuracy comparison for Severity Level
axes[1, 0].bar(all_model_names, all_severity_level_acc, color='lightgreen', alpha=0.7)
axes[1, 0].set_title('Severity Level Prediction - Accuracy Comparison')
axes[1, 0].set_ylabel('Accuracy')
axes[1, 0].tick_params(axis='x', rotation=45)
axes[1, 0].grid(True, alpha=0.3)

# F1 Score comparison for Severity Level
axes[1, 1].bar(all_model_names, all_severity_level_f1, color='gold', alpha=0.7)
axes[1, 1].set_title('Severity Level Prediction - F1 Score Comparison')
axes[1, 1].set_ylabel('F1 Score')
axes[1, 1].tick_params(axis='x', rotation=45)
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('comprehensive_model_comparison.png', dpi=300, bbox_inches='tight')
plt.show()
print("Comprehensive model comparison saved as comprehensive_model_comparison.png")

# 8. Detailed Algorithm Descriptions and Analysis
print("\nStep 8: Detailed Algorithm Descriptions and Analysis")
print("-" * 50)

def print_algorithm_descriptions():
    """Print detailed descriptions of all algorithms used"""

    print("\n" + "="*80)
    print("COMPREHENSIVE ALGORITHM DESCRIPTIONS AND ANALYSIS")
    print("="*80)

    print("\n1. SUPPORT VECTOR MACHINE (SVM)")
    print("-" * 40)
    print("""
    Description:
    SVM is a powerful supervised learning algorithm that finds the optimal hyperplane
    to separate different classes in high-dimensional space. It uses kernel functions
    to transform data into higher dimensions where linear separation becomes possible.

    Key Features:
    - Uses RBF (Radial Basis Function) kernel for non-linear classification
    - Effective in high-dimensional spaces
    - Memory efficient as it uses support vectors
    - Robust to overfitting, especially in high-dimensional space

    Advantages:
    - Excellent performance on complex, non-linear problems
    - Works well with limited data
    - Robust to outliers

    Disadvantages:
    - Computationally expensive for large datasets
    - Sensitive to feature scaling
    - No probabilistic output (though we enabled probability=True)
    """)

    print("\n2. NAIVE BAYES")
    print("-" * 40)
    print("""
    Description:
    Naive Bayes is a probabilistic classifier based on Bayes' theorem with the
    "naive" assumption of conditional independence between features. Despite this
    strong assumption, it often performs surprisingly well in practice.

    Key Features:
    - Assumes Gaussian distribution of features (GaussianNB)
    - Fast training and prediction
    - Requires small training datasets
    - Handles multi-class classification naturally

    Advantages:
    - Fast and simple
    - Works well with small datasets
    - Not sensitive to irrelevant features
    - Good baseline model

    Disadvantages:
    - Strong independence assumption rarely holds in reality
    - Can be outperformed by more sophisticated methods
    - Sensitive to skewed data
    """)

    print("\n3. LOGISTIC REGRESSION")
    print("-" * 40)
    print("""
    Description:
    Logistic Regression uses the logistic function to model the probability of
    class membership. It's a linear classifier that can handle multi-class
    problems using multinomial approach.

    Key Features:
    - Uses multinomial approach for multi-class classification
    - Provides probabilistic outputs
    - Linear decision boundaries
    - Regularization to prevent overfitting

    Advantages:
    - Interpretable coefficients
    - Fast training and prediction
    - No tuning of hyperparameters required
    - Probabilistic output

    Disadvantages:
    - Assumes linear relationship between features and log-odds
    - Sensitive to outliers
    - Requires large sample sizes for stable results
    """)

    print("\n4. XGBOOST (EXTREME GRADIENT BOOSTING)")
    print("-" * 40)
    print("""
    Description:
    XGBoost is an optimized gradient boosting framework that builds models
    sequentially, where each new model corrects errors made by previous models.
    It's known for its performance in machine learning competitions.

    Key Features:
    - Gradient boosting with advanced regularization
    - Handles missing values automatically
    - Built-in cross-validation
    - Feature importance calculation

    Advantages:
    - Excellent predictive performance
    - Handles mixed data types well
    - Built-in regularization prevents overfitting
    - Provides feature importance

    Disadvantages:
    - Can be prone to overfitting with small datasets
    - Requires hyperparameter tuning
    - Less interpretable than linear models
    """)

    print("\n5. LIGHTGBM (LIGHT GRADIENT BOOSTING MACHINE)")
    print("-" * 40)
    print("""
    Description:
    LightGBM is a gradient boosting framework that uses tree-based learning
    algorithms. It's designed to be distributed and efficient with faster
    training speed and higher efficiency.

    Key Features:
    - Leaf-wise tree growth (vs level-wise in traditional methods)
    - Faster training speed
    - Lower memory usage
    - Better accuracy than many other boosting tools

    Advantages:
    - Very fast training
    - High accuracy
    - Memory efficient
    - Handles large datasets well

    Disadvantages:
    - Can overfit with small datasets
    - Sensitive to hyperparameters
    - May require more careful tuning
    """)

    print("\n6. CONVOLUTIONAL NEURAL NETWORK (CNN)")
    print("-" * 40)
    print("""
    Description:
    CNN adapted for tabular data uses 1D convolutions to detect local patterns
    in feature sequences. Originally designed for image processing, we've adapted
    it for drug interaction prediction by treating features as a 1D sequence.

    Key Features:
    - 1D convolutions for pattern detection
    - Max pooling for dimensionality reduction
    - Multiple convolutional layers with different filter sizes
    - Dropout for regularization

    Advantages:
    - Can detect local patterns in feature space
    - Translation invariant (patterns detected regardless of position)
    - Hierarchical feature learning
    - Good for sequential or spatial data

    Disadvantages:
    - Requires large amounts of data
    - Computationally expensive
    - Many hyperparameters to tune
    - May not be optimal for tabular data
    """)

    print("\n7. LONG SHORT-TERM MEMORY (LSTM)")
    print("-" * 40)
    print("""
    Description:
    LSTM is a type of recurrent neural network capable of learning long-term
    dependencies. For drug interactions, we use it to model sequential
    relationships between drug features.

    Key Features:
    - Memory cells to store information
    - Forget, input, and output gates
    - Handles vanishing gradient problem
    - Bidirectional processing capability

    Advantages:
    - Excellent for sequential data
    - Can learn long-term dependencies
    - Handles variable-length sequences
    - Good for temporal patterns

    Disadvantages:
    - Computationally expensive
    - Requires large datasets
    - Many parameters to train
    - May be overkill for non-sequential tabular data
    """)

    print("\n8. BERT-INSPIRED TRANSFORMER MODEL")
    print("-" * 40)
    print("""
    Description:
    A transformer-inspired architecture adapted for tabular data that simulates
    the attention mechanism of BERT. It uses multiple attention heads to focus
    on different aspects of the input features.

    Key Features:
    - Multi-head attention simulation
    - Residual connections
    - Feed-forward networks
    - Dense layers instead of true attention (adapted for tabular data)

    Advantages:
    - Can model complex feature interactions
    - Attention mechanism highlights important features
    - Parallel processing capability
    - State-of-the-art architecture adaptation

    Disadvantages:
    - Very computationally expensive
    - Requires large amounts of data
    - Many parameters to optimize
    - Complex architecture may be unnecessary for simple problems
    """)

# Print algorithm descriptions
print_algorithm_descriptions()

# 9. Feature Importance Analysis
print("\nStep 9: Feature Importance Analysis")
print("-" * 50)

# Use XGBoost for feature importance analysis
print("\nTop 10 important features for Interaction Type prediction:")
feature_importance_type = pd.DataFrame({
    'Feature': X.columns,
    'Importance': xgb_type_model.feature_importances_
})
feature_importance_type = feature_importance_type.sort_values('Importance', ascending=False).head(10)
print(feature_importance_type)

print("\nTop 10 important features for Severity Level prediction:")
feature_importance_level = pd.DataFrame({
    'Feature': X.columns,
    'Importance': xgb_level_model.feature_importances_
})
feature_importance_level = feature_importance_level.sort_values('Importance', ascending=False).head(10)
print(feature_importance_level)

# Plot feature importance
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# Feature importance for interaction type
axes[0].barh(feature_importance_type['Feature'], feature_importance_type['Importance'])
axes[0].set_title('Top 10 Features - Interaction Type Prediction')
axes[0].set_xlabel('Importance')
axes[0].invert_yaxis()

# Feature importance for severity level
axes[1].barh(feature_importance_level['Feature'], feature_importance_level['Importance'])
axes[1].set_title('Top 10 Features - Severity Level Prediction')
axes[1].set_xlabel('Importance')
axes[1].invert_yaxis()

plt.tight_layout()
plt.savefig('feature_importance_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
print("Feature importance analysis saved as feature_importance_analysis.png")

# 10. Comprehensive Model Comparison and Analysis
print("\nStep 10: Comprehensive Model Comparison and Analysis")
print("-" * 50)

def print_comprehensive_analysis():
    """Print comprehensive analysis of all models"""

    print("\n" + "="*80)
    print("COMPREHENSIVE MODEL PERFORMANCE ANALYSIS")
    print("="*80)

    # Create performance summary
    performance_summary = pd.DataFrame({
        'Algorithm': all_model_names,
        'Type_Accuracy': all_interaction_type_acc,
        'Type_F1': all_interaction_type_f1,
        'Level_Accuracy': all_severity_level_acc,
        'Level_F1': all_severity_level_f1,
        'Avg_Performance': [(acc1 + f1_1 + acc2 + f1_2) / 4 for acc1, f1_1, acc2, f1_2 in
                           zip(all_interaction_type_acc, all_interaction_type_f1,
                               all_severity_level_acc, all_severity_level_f1)]
    })

    # Sort by average performance
    performance_summary = performance_summary.sort_values('Avg_Performance', ascending=False)

    print("\nRANKED MODEL PERFORMANCE (by Average Performance):")
    print("-" * 60)
    for idx, row in performance_summary.iterrows():
        print(f"{row['Algorithm']:15} | Avg: {row['Avg_Performance']:.4f} | "
              f"Type: {row['Type_F1']:.4f} | Level: {row['Level_F1']:.4f}")

    print("\n" + "="*80)
    print("ALGORITHM CATEGORY ANALYSIS")
    print("="*80)

    # Categorize algorithms
    traditional_ml = ['SVM', 'Naive Bayes', 'Logistic Regression']
    ensemble_methods = ['XGBoost', 'LightGBM']
    deep_learning = ['CNN', 'LSTM', 'BERT-Inspired']

    def get_category_performance(category_models):
        category_performance = performance_summary[performance_summary['Algorithm'].isin(category_models)]
        return category_performance['Avg_Performance'].mean()

    traditional_avg = get_category_performance(traditional_ml)
    ensemble_avg = get_category_performance(ensemble_methods)
    deep_learning_avg = get_category_performance(deep_learning)

    print(f"\nTraditional ML Average Performance: {traditional_avg:.4f}")
    print(f"Ensemble Methods Average Performance: {ensemble_avg:.4f}")
    print(f"Deep Learning Average Performance: {deep_learning_avg:.4f}")

    # Determine best category
    categories = {
        'Traditional ML': traditional_avg,
        'Ensemble Methods': ensemble_avg,
        'Deep Learning': deep_learning_avg
    }
    best_category = max(categories, key=categories.get)
    print(f"\nBest Performing Category: {best_category}")

    print("\n" + "="*80)
    print("DETAILED RECOMMENDATIONS")
    print("="*80)

    print("\n1. BEST OVERALL MODELS:")
    top_3_overall = performance_summary.head(3)
    for idx, row in top_3_overall.iterrows():
        print(f"   {idx+1}. {row['Algorithm']} (Avg Performance: {row['Avg_Performance']:.4f})")

    print("\n2. TASK-SPECIFIC RECOMMENDATIONS:")
    best_type_idx = performance_summary['Type_F1'].idxmax()
    best_level_idx = performance_summary['Level_F1'].idxmax()

    print(f"   - For Interaction Type Prediction: {performance_summary.loc[best_type_idx, 'Algorithm']}")
    print(f"     (F1 Score: {performance_summary.loc[best_type_idx, 'Type_F1']:.4f})")
    print(f"   - For Severity Level Prediction: {performance_summary.loc[best_level_idx, 'Algorithm']}")
    print(f"     (F1 Score: {performance_summary.loc[best_level_idx, 'Level_F1']:.4f})")

    print("\n3. COMPUTATIONAL EFFICIENCY CONSIDERATIONS:")
    print("   - Fastest: Naive Bayes, Logistic Regression")
    print("   - Balanced Speed/Performance: XGBoost, LightGBM")
    print("   - Highest Performance (but slower): Deep Learning models")

    print("\n4. DATA SIZE RECOMMENDATIONS:")
    print("   - Small datasets (<1000 samples): SVM, Naive Bayes")
    print("   - Medium datasets (1000-10000): XGBoost, LightGBM")
    print("   - Large datasets (>10000): Deep Learning models")

    print("\n5. INTERPRETABILITY RANKING:")
    print("   1. Logistic Regression (highest interpretability)")
    print("   2. Naive Bayes")
    print("   3. XGBoost/LightGBM (feature importance)")
    print("   4. SVM")
    print("   5. Deep Learning models (lowest interpretability)")

# Print comprehensive analysis
print_comprehensive_analysis()

# 11. Novel Drug Prediction Framework
print("\nStep 11: Novel Drug Prediction Framework")
print("-" * 50)

# Function to predict interactions for a new drug pair
def predict_interaction(drug_a_features, drug_b_features, type_model, level_model, scaler):
    # Combine features
    combined_features = np.concatenate([drug_a_features, drug_b_features])

    # Scale features
    scaled_features = scaler.transform([combined_features])

    # Predict interaction type and severity
    interaction_type_pred = type_model.predict(scaled_features)[0]
    interaction_level_pred = level_model.predict(scaled_features)[0]

    # Convert predictions back to original labels
    interaction_type = label_encoder_type.inverse_transform([interaction_type_pred])[0]
    interaction_level = label_encoder_level.inverse_transform([interaction_level_pred])[0]

    return interaction_type, interaction_level

print("Novel drug prediction framework implemented.")
print("This framework can be used to predict interactions for new drug pairs based on their features.")

# ============================================================================
# PARAMETER TUNING AND RESULTS MANAGEMENT
# ============================================================================

print("\n" + "="*80)
print("PARAMETER TUNING AND RESULTS MANAGEMENT")
print("="*80)

# Save current parameters
print("\nSaving current parameters...")
params_file = model_params.save_parameters()

# Save experiment results
print("Saving experiment results...")
results_file = results_recorder.save_results()

# Create results DataFrame
print("Creating results summary...")
results_df = results_recorder.create_results_dataframe()
if not results_df.empty:
    print("\nExperiment Results Summary:")
    print(results_df[['Model', 'Target', 'Accuracy', 'F1_Score', 'Training_Time']].to_string(index=False))

    # Save results to CSV
    csv_filename = f"experiment_results_summary_{results_recorder.experiment_id}.csv"
    results_df.to_csv(csv_filename, index=False)
    print(f"\nResults summary saved to {csv_filename}")

# Get best results
print("\nBest Results for Each Model and Target:")
best_results = results_recorder.get_best_results()
for key, result in best_results.items():
    print(f"{key}: Accuracy={result['metrics']['accuracy']:.4f}, F1={result['metrics']['f1_score']:.4f}")

print("\n" + "="*80)
print("PARAMETER MODIFICATION GUIDE")
print("="*80)

print("""
To modify parameters for better results, you can:

1. EDIT PARAMETERS DIRECTLY IN CODE:
   - Modify the ModelParameters class __init__ method
   - Change values in model_params.svm_params, model_params.xgb_params, etc.

2. LOAD PARAMETERS FROM FILE:
   - Edit the saved JSON file: {}
   - Load with: model_params.load_parameters('filename.json')

3. MODIFY SPECIFIC ALGORITHM PARAMETERS:

   SVM Parameters to try:
   - C: [0.1, 1.0, 10.0, 100.0] (regularization strength)
   - kernel: ['linear', 'rbf', 'poly'] (kernel type)
   - gamma: ['scale', 'auto', 0.001, 0.01, 0.1, 1.0] (kernel coefficient)

   XGBoost Parameters to try:
   - learning_rate: [0.01, 0.1, 0.2, 0.3] (step size shrinkage)
   - max_depth: [3, 6, 10, 15] (maximum tree depth)
   - n_estimators: [50, 100, 200, 500] (number of trees)
   - subsample: [0.8, 0.9, 1.0] (subsample ratio)

   LightGBM Parameters to try:
   - learning_rate: [0.01, 0.05, 0.1, 0.2] (boosting learning rate)
   - num_leaves: [15, 31, 63, 127] (max tree leaves)
   - min_child_samples: [10, 20, 30, 50] (min data in leaf)

   CNN Parameters to try:
   - filters_1: [32, 64, 128] (number of filters in first layer)
   - dropout_conv: [0.2, 0.3, 0.4, 0.5] (dropout rate)
   - learning_rate: [0.0001, 0.001, 0.01] (optimizer learning rate)
   - batch_size: [16, 32, 64] (training batch size)

   LSTM Parameters to try:
   - lstm_units_1: [64, 128, 256] (LSTM units in first layer)
   - dropout: [0.2, 0.3, 0.4, 0.5] (dropout rate)
   - learning_rate: [0.0001, 0.001, 0.01] (optimizer learning rate)

4. EXAMPLE PARAMETER MODIFICATION:

   # Modify SVM parameters for better performance
   model_params.svm_params['C'] = 10.0
   model_params.svm_params['gamma'] = 0.01

   # Modify XGBoost parameters
   model_params.xgb_params['learning_rate'] = 0.1
   model_params.xgb_params['max_depth'] = 10
   model_params.xgb_params['n_estimators'] = 200

   # Then re-run the training sections

5. AUTOMATED PARAMETER TUNING:
   - Use GridSearchCV or RandomizedSearchCV for systematic tuning
   - Implement Bayesian optimization for efficient parameter search
   - Use cross-validation for robust parameter evaluation

""".format(params_file))

# 12. Multi-Drug Interaction Framework
print("\nStep 12: Multi-Drug Interaction Framework")
print("-" * 50)

# Function to predict interactions for multiple drugs
def predict_multi_drug_interaction(drug_features_list, type_model, level_model, scaler):
    # For multi-drug interactions, we'll use a pairwise approach
    interaction_types = []
    interaction_levels = []

    # Predict pairwise interactions
    for i in range(len(drug_features_list)):
        for j in range(i+1, len(drug_features_list)):
            drug_a_features = drug_features_list[i]
            drug_b_features = drug_features_list[j]

            interaction_type, interaction_level = predict_interaction(
                drug_a_features, drug_b_features, type_model, level_model, scaler
            )

            interaction_types.append(interaction_type)
            interaction_levels.append(interaction_level)

    # Aggregate results (taking the most severe interaction)
    severity_order = {
        'Minor': 1,
        'Moderate': 2,
        'Major': 3,
        'Unknown': 0
    }

    # Find the most common interaction type
    most_common_type = Counter(interaction_types).most_common(1)[0][0]

    # Find the most severe level
    most_severe_level = max(interaction_levels, key=lambda x: severity_order.get(x, 0))

    return most_common_type, most_severe_level

print("Multi-drug interaction framework implemented.")
print("This framework can predict interactions among multiple drugs by analyzing pairwise interactions.")

# 13. Final Summary and Conclusion
print("\nStep 13: Final Summary and Conclusion")
print("-" * 50)

print("\n" + "="*80)
print("DRUG-DRUG INTERACTION PREDICTION MODEL - FINAL SUMMARY")
print("="*80)

print(f"\nBest Overall Model for Interaction Type prediction: {best_type_model}")
print(f"Best Overall Model for Severity Level prediction: {best_level_model}")

print("\nKey Achievements:")
print("✓ Implemented 8 different machine learning algorithms")
print("✓ Comprehensive comparison including traditional ML, ensemble, and deep learning")
print("✓ Generated detailed visualizations and confusion matrices")
print("✓ Feature importance analysis")
print("✓ Novel drug prediction framework")
print("✓ Multi-drug interaction prediction capability")

print("\nModel Capabilities:")
print("1. Predict interaction types between known drugs")
print("2. Predict severity levels of interactions")
print("3. Handle interactions with novel drugs")
print("4. Analyze interactions among multiple drugs")
print("5. Provide feature importance insights")

# Save comprehensive summary report
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
summary_filename = f"ddi_comprehensive_summary_{timestamp}.txt"

with open(summary_filename, 'w') as f:
    f.write("=== Drug-Drug Interaction Prediction - Comprehensive Summary ===\n")
    f.write(f"Date and Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    f.write("=== Dataset Information ===\n")
    f.write(f"Dataset Shape: {df.shape}\n")
    f.write(f"Number of Features: {X.shape[1]}\n")
    f.write(f"Number of Interaction Types: {len(label_encoder_type.classes_)}\n")
    f.write(f"Number of Severity Levels: {len(label_encoder_level.classes_)}\n\n")

    f.write("=== Comprehensive Model Performance Comparison ===\n")
    f.write(comprehensive_comparison_df.to_string(index=False))
    f.write("\n\n")

    f.write("=== Best Models ===\n")
    f.write(f"Best model for Interaction Type prediction: {best_type_model}\n")
    f.write(f"Best model for Severity Level prediction: {best_level_model}\n\n")

    f.write("=== Algorithm Categories Performance ===\n")
    f.write("Traditional ML: SVM, Naive Bayes, Logistic Regression\n")
    f.write("Ensemble Methods: XGBoost, LightGBM\n")
    f.write("Deep Learning: CNN, LSTM, BERT-Inspired\n\n")

    f.write("=== Feature Importance (Top 10) ===\n")
    f.write("Interaction Type Prediction:\n")
    f.write(feature_importance_type.to_string(index=False))
    f.write("\n\nSeverity Level Prediction:\n")
    f.write(feature_importance_level.to_string(index=False))
    f.write("\n\n")

    f.write("=== Class Mappings ===\n")
    f.write("Interaction Types:\n")
    for idx, label in enumerate(label_encoder_type.classes_):
        f.write(f"Class {idx}: {label}\n")
    f.write("\nSeverity Levels:\n")
    for idx, label in enumerate(label_encoder_level.classes_):
        f.write(f"Class {idx}: {label}\n")

    f.write("\n=== Generated Visualizations ===\n")
    f.write("- Confusion matrices for top performing models\n")
    f.write("- Training history plots for deep learning models\n")
    f.write("- Comprehensive model comparison charts\n")
    f.write("- Feature importance analysis plots\n")

print(f"\nComprehensive summary saved to {summary_filename}")
print("\n" + "="*80)
print("ALL ANALYSIS COMPLETE - READY FOR KAGGLE EXECUTION!")
print("="*80)
